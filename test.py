from pathlib import Path
from typing import Annotated

from pydantic import BaseModel, Field
from pydantic_settings import (
    BaseSettings,
    JsonConfigSettingsSource,
    PydanticBaseSettingsSource,
    SettingsConfigDict,
)


class Layer(BaseModel):
    """Represents a layer of buttons."""

    name: str
    description: str


class Config(BaseSettings):
    """Main configuration using Pydantic Settings."""

    layers: Annotated[dict[str, Layer], Field(...)]

    model_config = SettingsConfigDict(
        json_file="config.json",
        extra="ignore",
    )

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,
        env_settings: PydanticBaseSettingsSource,
        dotenv_settings: PydanticBaseSettingsSource,
        file_secret_settings: PydanticBaseSettingsSource,
    ) -> tuple[PydanticBaseSettingsSource, ...]:
        return (JsonConfigSettingsSource(settings_cls, Path("config.json")),)


if __name__ == "__main__":
    # Test
    print(Config().model_dump_json())
