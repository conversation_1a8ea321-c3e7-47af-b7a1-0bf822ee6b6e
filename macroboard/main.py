"""BoopBoard - a stream deck-style app, built with NiceGUI."""

from nicegui import ui

# Prevent pull-to-refresh and overscroll on touch
ui.add_css("""
body, html, #nicegui-body {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
    height: 100% !important;
}

body {
    overscroll-behavior: none !important;
}
""")

# Automatically go into fullscreen on touch
ui.add_body_html("""
<script>
document.addEventListener('touchstart', function(event) {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    }
}, { passive: false });
</script>
""")


def handle_button_action(action_name: str) -> None:
    """Handle button actions - replace with actual functionality."""
    ui.notify(f"{action_name} activated!", type="info")


def handle_layer_change(layer_num: int) -> None:
    """Handle layer changes - replace with actual functionality."""
    ui.notify(f"Switched to Layer {layer_num}", type="positive")


with ui.element("div").classes("flex w-full h-screen overflow-hidden touch-none"):
    # Layer Sidebar
    with ui.element("div").classes(
        "w-16 bg-base-300 p-2 flex flex-col items-center justify-center gap-4"
    ):
        # Default Layer
        ui.icon("view_quilt").classes("text-4xl text-white")

    # Main Content
    with (
        ui.element("div").classes(
            "grow bg-base-100 pb-4 flex flex-col overflow-hidden justify-items-center"
        ),
        ui.grid(rows=4, columns=4).classes("w-full h-full gap-2 p-4"),
    ):
        for i in range(1, 17):  # 4x4 = 16 buttons
            ui.button(f"Button {i}").classes("w-full h-full min-h-0")


ui.run(
    dark=None,
    title="BoopBoard",
    favicon="🗿",
    viewport="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no",
)
