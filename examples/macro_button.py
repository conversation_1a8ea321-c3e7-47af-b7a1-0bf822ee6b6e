from collections.abc import Callable
from nicegui import ui


def macro_button(
    name: str,
    image_url: str | None = None,
    icon: str | None = None,
    text: str | None = None,
    on_click: Callable | None = None,
    bg_color: str = "bg-gray-700",
    text_color: str = "text-white",
) -> None:
    """Create a stream deck-style macro button.

    Args:
        name: Button identifier
        image_url: URL or path to background image
        icon: Material icon name (used if no image provided)
        text: Text to display on button (defaults to name)
        on_click: Callback function when button is clicked
        bg_color: Background color class (used if no image)
        text_color: Text color class
    """
    display_text = text or name

    # Base button classes for stream deck style
    base_classes = (
        "w-32 h-32 relative overflow-hidden "
        "border-2 border-gray-600 hover:border-gray-400 "
        "transition-all duration-200 hover:scale-105 "
        "flex flex-col items-center justify-center "
        "rounded-lg shadow-lg"
    )

    # Add background styling
    if image_url:
        button_classes = f"{base_classes} bg-cover bg-center"
        style = f"background-image: url('{image_url}');"
    else:
        button_classes = f"{base_classes} {bg_color}"
        style = ""

    with ui.button().classes(button_classes).style(style) as button:
        if on_click:
            button.on_click(on_click)

        # Add overlay for better text readability on images
        if image_url:
            ui.element("div").classes("absolute inset-0 bg-black bg-opacity-30")

        # Content container
        with ui.element("div").classes(
            "relative z-10 flex flex-col items-center justify-center h-full w-full p-2"
        ):
            # Icon or default icon
            if icon:
                ui.icon(icon).classes(f"text-2xl {text_color} mb-1")
            elif not image_url:
                ui.icon("touch_app").classes(f"text-2xl {text_color} mb-1")

            # Text label
            if display_text:
                ui.label(display_text).classes(
                    f"text-xs {text_color} text-center font-medium "
                    "leading-tight break-words"
                )
