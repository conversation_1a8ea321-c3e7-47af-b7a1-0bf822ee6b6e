"""Configuration management for BoopBoard using Pydantic Settings."""

from typing import Literal

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class ButtonAction(BaseModel):
    """Represents an action that a button can perform."""

    type: Literal["hotkey", "media", "obs", "system", "layer", "command"]
    command: str | None = None
    keys: list[str] | None = None
    target: str | None = None
    scene: str | None = None
    source: str | None = None
    args: dict[str, str] | None = None


class Button(BaseModel):
    """Represents a button configuration."""

    position: int
    name: str
    text: str | None = None
    icon: str | None = None
    image_url: str | None = None
    bg_color: str = "bg-gray-700"
    text_color: str = "text-white"
    action: ButtonAction | None = None


class Layer(BaseModel):
    """Represents a layer of buttons."""

    name: str
    description: str
    buttons: list[Button] = Field(default_factory=list)

    def get_button_at_position(self, position: int) -> Button | None:
        """Get button at specific position."""
        return next((btn for btn in self.buttons if btn.position == position), None)


class GridConfig(BaseModel):
    """Grid layout configuration."""

    small: int = 3
    medium: int = 4
    large: int = 6


class ButtonSizeConfig(BaseModel):
    """Button size configuration."""

    width: int = 32
    height: int = 32


class AppConfig(BaseModel):
    """Application configuration."""

    title: str = "BoopBoard"
    default_layer: str = "main"
    grid_cols: GridConfig = Field(default_factory=GridConfig)
    button_size: ButtonSizeConfig = Field(default_factory=ButtonSizeConfig)


class Config(BaseSettings):
    """Main configuration using Pydantic Settings."""

    model_config = SettingsConfigDict(
        toml_file="config.toml",
        toml_file_encoding="utf-8",
        extra="ignore",
    )

    app: AppConfig = Field(default_factory=AppConfig)
    layers: dict[str, Layer] = Field(default_factory=dict)

    def __init__(self, **kwargs) -> None:  # type: ignore[no-untyped-def]
        """Initialize config and set current layer."""
        super().__init__(**kwargs)
        self.current_layer = self.app.default_layer

    def get_current_layer(self) -> Layer:
        """Get the currently active layer."""
        return self.layers.get(self.current_layer, self.layers[self.app.default_layer])

    def switch_layer(self, layer_id: str) -> bool:
        """Switch to a different layer."""
        if layer_id in self.layers:
            self.current_layer = layer_id
            return True
        return False

    def get_button_at_position(self, position: int) -> Button | None:
        """Get button at specific position in current layer."""
        return self.get_current_layer().get_button_at_position(position)

    def get_grid_classes(self) -> str:
        """Get Tailwind grid classes based on configuration."""
        cols = self.app.grid_cols
        return (
            f"grid-cols-{cols.small} sm:grid-cols-{cols.medium} lg:grid-cols-{cols.large}"
        )

    def get_button_size_classes(self) -> str:
        """Get Tailwind size classes for buttons."""
        size = self.app.button_size
        return f"w-{size.width} h-{size.height}"


# Global config instance - lazy loaded
_config_instance = None


def get_config() -> Config:
    """Get the global config instance (lazy loaded)."""
    global _config_instance
    if _config_instance is None:
        _config_instance = Config()
    return _config_instance


# For backward compatibility
config = get_config()
