"""System extension for BoopBoard - handles system-level commands."""

import subprocess
import sys
from collections.abc import Callable


class SystemExtension:
    """Extension for system-level commands like brightness, volume, etc."""

    def get_handlers(self) -> dict[str, Callable]:
        """Get all available system command handlers."""
        return {
            "brightness_up": self.brightness_up,
            "brightness_down": self.brightness_down,
            "volume_up": self.volume_up,
            "volume_down": self.volume_down,
            "mute_toggle": self.mute_toggle,
            "screenshot": self.screenshot,
            "lock_screen": self.lock_screen,
            "sleep": self.sleep,
            "shutdown": self.shutdown,
            "restart": self.restart,
        }

    def _run_command(self, command: list[str]) -> bool:
        """Run a system command safely."""
        try:
            subprocess.run(command, check=True, capture_output=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError) as e:
            print(f"Command failed: {' '.join(command)} - {e}")
            return False

    def brightness_up(self, step: int = 10) -> bool:
        """Increase screen brightness."""
        if sys.platform == "linux":
            # Try different brightness control methods
            commands = [
                ["brightnessctl", "set", f"{step}%+"],
                ["xbacklight", "-inc", str(step)],
                ["light", "-A", str(step)],
            ]
        elif sys.platform == "darwin":  # macOS
            commands = [
                [
                    "osascript",
                    "-e",
                    f'tell application "System Events" to key code 144',
                ]  # F15 key
            ]
        else:  # Windows
            commands = [
                [
                    "powershell",
                    "-Command",
                    f"(Get-WmiObject -Namespace root/WMI -Class WmiMonitorBrightnessMethods).WmiSetBrightness(1, {min(100, step + 50)})",
                ]
            ]

        for cmd in commands:
            if self._run_command(cmd):
                return True
        return False

    def brightness_down(self, step: int = 10) -> bool:
        """Decrease screen brightness."""
        if sys.platform == "linux":
            commands = [
                ["brightnessctl", "set", f"{step}%-"],
                ["xbacklight", "-dec", str(step)],
                ["light", "-U", str(step)],
            ]
        elif sys.platform == "darwin":  # macOS
            commands = [
                [
                    "osascript",
                    "-e",
                    f'tell application "System Events" to key code 145',
                ]  # F14 key
            ]
        else:  # Windows
            commands = [
                [
                    "powershell",
                    "-Command",
                    f"(Get-WmiObject -Namespace root/WMI -Class WmiMonitorBrightnessMethods).WmiSetBrightness(1, {max(0, 50 - step)})",
                ]
            ]

        for cmd in commands:
            if self._run_command(cmd):
                return True
        return False

    def volume_up(self, step: int = 5) -> bool:
        """Increase system volume."""
        if sys.platform == "linux":
            commands = [
                ["pactl", "set-sink-volume", "@DEFAULT_SINK@", f"+{step}%"],
                ["amixer", "set", "Master", f"{step}%+"],
            ]
        elif sys.platform == "darwin":  # macOS
            commands = [
                [
                    "osascript",
                    "-e",
                    f"set volume output volume (output volume of (get volume settings) + {step})",
                ]
            ]
        else:  # Windows
            commands = [
                [
                    "powershell",
                    "-Command",
                    f"(New-Object -ComObject WScript.Shell).SendKeys([char]175)",
                ]  # Volume up key
            ]

        for cmd in commands:
            if self._run_command(cmd):
                return True
        return False

    def volume_down(self, step: int = 5) -> bool:
        """Decrease system volume."""
        if sys.platform == "linux":
            commands = [
                ["pactl", "set-sink-volume", "@DEFAULT_SINK@", f"-{step}%"],
                ["amixer", "set", "Master", f"{step}%-"],
            ]
        elif sys.platform == "darwin":  # macOS
            commands = [
                [
                    "osascript",
                    "-e",
                    f"set volume output volume (output volume of (get volume settings) - {step})",
                ]
            ]
        else:  # Windows
            commands = [
                [
                    "powershell",
                    "-Command",
                    f"(New-Object -ComObject WScript.Shell).SendKeys([char]174)",
                ]  # Volume down key
            ]

        for cmd in commands:
            if self._run_command(cmd):
                return True
        return False

    def mute_toggle(self) -> bool:
        """Toggle system mute."""
        if sys.platform == "linux":
            commands = [
                ["pactl", "set-sink-mute", "@DEFAULT_SINK@", "toggle"],
                ["amixer", "set", "Master", "toggle"],
            ]
        elif sys.platform == "darwin":  # macOS
            commands = [["osascript", "-e", "set volume with output muted"]]
        else:  # Windows
            commands = [
                [
                    "powershell",
                    "-Command",
                    "(New-Object -ComObject WScript.Shell).SendKeys([char]173)",
                ]  # Mute key
            ]

        for cmd in commands:
            if self._run_command(cmd):
                return True
        return False

    def screenshot(self, path: str = "~/screenshot.png") -> bool:
        """Take a screenshot."""
        if sys.platform == "linux":
            commands = [
                ["gnome-screenshot", "-f", path],
                ["scrot", path],
                ["import", "-window", "root", path],
            ]
        elif sys.platform == "darwin":  # macOS
            commands = [["screencapture", path]]
        else:  # Windows
            commands = [
                [
                    "powershell",
                    "-Command",
                    f"Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('%{{PRTSC}}')",
                ]
            ]

        for cmd in commands:
            if self._run_command(cmd):
                return True
        return False

    def lock_screen(self) -> bool:
        """Lock the screen."""
        if sys.platform == "linux":
            commands = [
                ["loginctl", "lock-session"],
                ["gnome-screensaver-command", "-l"],
                ["xdg-screensaver", "lock"],
            ]
        elif sys.platform == "darwin":  # macOS
            commands = [
                [
                    "osascript",
                    "-e",
                    'tell application "System Events" to keystroke "q" using {command down, control down}',
                ]
            ]
        else:  # Windows
            commands = [["rundll32.exe", "user32.dll,LockWorkStation"]]

        for cmd in commands:
            if self._run_command(cmd):
                return True
        return False

    def sleep(self) -> bool:
        """Put system to sleep."""
        if sys.platform == "linux":
            commands = [
                ["systemctl", "suspend"],
                ["pm-suspend"],
            ]
        elif sys.platform == "darwin":  # macOS
            commands = [["pmset", "sleepnow"]]
        else:  # Windows
            commands = [["rundll32.exe", "powrprof.dll,SetSuspendState", "0,1,0"]]

        for cmd in commands:
            if self._run_command(cmd):
                return True
        return False

    def shutdown(self) -> bool:
        """Shutdown the system."""
        if sys.platform == "linux":
            return self._run_command(["shutdown", "-h", "now"])
        elif sys.platform == "darwin":  # macOS
            return self._run_command(["sudo", "shutdown", "-h", "now"])
        else:  # Windows
            return self._run_command(["shutdown", "/s", "/t", "0"])

    def restart(self) -> bool:
        """Restart the system."""
        if sys.platform == "linux":
            return self._run_command(["shutdown", "-r", "now"])
        elif sys.platform == "darwin":  # macOS
            return self._run_command(["sudo", "shutdown", "-r", "now"])
        else:  # Windows
            return self._run_command(["shutdown", "/r", "/t", "0"])


def register() -> dict[str, Callable]:
    """Register the system extension."""
    return SystemExtension().get_handlers()
