"""Extension system for BoopBoard actions."""

import importlib
import logging
from collections.abc import Callable
from pathlib import Path
from typing import Any, TypedDict

logger = logging.getLogger(__name__)


class Extension(TypedDict):
    """TypedDict for extension registration."""

    name: str
    handlers: dict[str, Callable[..., Any]]


class Extensions:
    """Automatically registers all available extensions."""

    def __init__(self) -> None:
        """Initialize the extensions."""
        self._extensions: dict[str, dict[str, Callable[..., Any]]] = {}
        self._register_default_extensions()

    def _register_extension(self, ext_path: str) -> None:
        """Register a single extension."""
        try:
            ext_module = importlib.import_module(ext_path)
            ext_instance: Extension = ext_module.register()

            if any(key not in ext_instance for key in Extension.__required_keys__):
                logger.warning(f"Extension {ext_path} returned invalid registration")
                return

            self._extensions[ext_instance["name"]] = ext_instance["handlers"]
            logger.info(f"Loaded extension: {ext_path}")

        except (ModuleNotFoundError, <PERSON>mportError, AttributeError) as e:
            logger.error(f"Failed to load extension {ext_path}: {e}")
        except Exception as e:
            logger.error(f"Unexpected error loading extension {ext_path}: {e}")

    def _register_default_extensions(self) -> None:
        """Register all default extensions."""
        ext_folder = Path(__file__).parent.resolve()

        for ext_file in ext_folder.glob("*.py"):
            if ext_file.name == "__init__.py":
                continue

            self._register_extension(ext_file.stem)

    def execute(self, action_type: str, command: str, **kwargs: Any) -> Any:
        """Execute an action command."""
        if action_type not in self._extensions:
            raise ValueError(f"Unknown action type: {action_type}")

        handlers = self._extensions[action_type]
        if command not in handlers:
            available = ", ".join(handlers.keys())
            raise ValueError(
                f"Unknown command '{command}' for '{action_type}'. Available: {available}"
            )

        try:
            return handlers[command](**kwargs)
        except Exception as e:
            logger.error(f"Failed to execute {action_type}.{command}: {e}")
            raise

    def get_available_actions(self) -> dict[str, list[str]]:
        """Get all available actions grouped by type."""
        return {
            action_type: list(handlers.keys())
            for action_type, handlers in self._extensions.items()
        }

    def is_action_available(self, action_type: str, command: str) -> bool:
        """Check if a specific action is available."""
        return (
            action_type in self._extensions and command in self._extensions[action_type]
        )


# Global extensions instance
extensions = Extensions()
