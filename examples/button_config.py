"""
Example configuration for stream deck-style buttons.
This shows how to create different types of buttons with various features.
"""

from lib.macro_button import macro_button


def create_example_buttons():
    """Create example buttons demonstrating different features."""
    
    # Button with image background
    macro_button(
        name="obs_stream",
        image_url="https://obsproject.com/assets/images/new_icon_small-r.png",
        text="Start Stream",
        on_click=lambda: start_obs_stream(),
        text_color="text-white"
    )
    
    # Button with icon and custom colors
    macro_button(
        name="discord_mute",
        icon="mic_off",
        text="Mute",
        on_click=lambda: toggle_discord_mute(),
        bg_color="bg-indigo-600",
        text_color="text-white"
    )
    
    # Button with local image
    macro_button(
        name="spotify",
        image_url="/static/images/spotify-logo.png",
        text="Play/Pause",
        on_click=lambda: toggle_spotify(),
    )
    
    # Simple action button
    macro_button(
        name="volume_up",
        icon="volume_up",
        text="Vol +",
        on_click=lambda: adjust_volume(5),
        bg_color="bg-blue-600"
    )


def start_obs_stream():
    """Start OBS streaming."""
    # Add your OBS integration here
    print("Starting OBS stream...")


def toggle_discord_mute():
    """Toggle Discord mute."""
    # Add your Discord integration here
    print("Toggling Discord mute...")


def toggle_spotify():
    """Toggle Spotify playback."""
    # Add your Spotify integration here
    print("Toggling Spotify...")


def adjust_volume(amount: int):
    """Adjust system volume."""
    # Add your volume control here
    print(f"Adjusting volume by {amount}")
